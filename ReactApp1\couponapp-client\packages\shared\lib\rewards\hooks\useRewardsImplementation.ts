import { useState, useCallback, useMemo } from 'react';
import { UseRewardsReturn, RewardLoadingState, RewardMechanics, GameEventData } from '../types';
import { RewardsManager } from '../RewardsManager';

export const useRewardsImplementation = (
  gameWidgetId: string,
  rewardMechanics: RewardMechanics[]
): UseRewardsReturn => {
  const [loadingState, setLoadingState] = useState<RewardLoadingState>({
    isLoading: false,
    operation: null,
  });

  // Create a new RewardsManager instance for this specific game widget
  const rewardsManager = useMemo(() => 
    new RewardsManager(gameWidgetId, rewardMechanics), 
    [gameWidgetId, rewardMechanics]
  );

  const handleGameEvent = useCallback(async (
    triggerType: 'round_start' | 'round_finish' | 'game_finish',
    data: GameEventData
  ) => {
    // For round_start, don't show loading state since it's non-blocking
    if (triggerType !== 'round_start') {
      setLoadingState({ isLoading: true, operation: 'rolling' });
    }

    try {
      const rewardRoll = await rewardsManager.handleGameEvent(triggerType, data);
      return rewardRoll;
    } finally {
      if (triggerType !== 'round_start') {
        setLoadingState({ isLoading: false, operation: null });
      }
    }
  }, [rewardsManager]);

  const getRewardByRoundId = useCallback(async (roundId: string) => {
    setLoadingState({ isLoading: true, operation: 'fetching' });
    
    try {
      return await rewardsManager.getRewardByRoundId(roundId);
    } finally {
      setLoadingState({ isLoading: false, operation: null });
    }
  }, [rewardsManager]);

  const getRewardHistory = useCallback(async () => {
    setLoadingState({ isLoading: true, operation: 'fetching' });
    
    try {
      return await rewardsManager.getRewardHistory();
    } finally {
      setLoadingState({ isLoading: false, operation: null });
    }
  }, [rewardsManager]);

  return {
    rewardHistory: { rewardPoolId: gameWidgetId, rolls: [] },
    loadingState,
    handleGameEvent,
    getRewardByRoundId,
    getRewardHistory,
  };
};
